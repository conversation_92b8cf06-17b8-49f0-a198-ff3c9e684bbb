<!-- Example Visitor List Section -->
<!-- You can add this anywhere in your HTML where you want to display visitor names -->

<section class="py-8 bg-white dark:bg-gray-800">
  <div class="container mx-auto px-6 md:px-12">
    <div class="max-w-md mx-auto">
      <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
        Recent Visitors
      </h3>
      
      <!-- Visitor List Container -->
      <div id="visitorList" class="space-y-2">
        <!-- Visitor names will be dynamically inserted here -->
        <!-- Default content when no visitors yet -->
        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
          <i class="ri-user-line ri-2x mb-2"></i>
          <p>No recent visitors yet</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Alternative: Sidebar Visitor List -->
<div class="fixed right-4 top-20 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-h-96 overflow-y-auto">
  <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-3">Recent Contacts</h4>
  <div id="visitorList" class="space-y-2">
    <!-- Visitor names will appear here -->
  </div>
</div>

<!-- Alternative: Simple List in Footer -->
<div class="bg-gray-100 dark:bg-gray-900 p-4">
  <div class="container mx-auto">
    <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Recent Contacts:</h4>
    <div id="visitorList" class="flex flex-wrap gap-2">
      <!-- Visitor names as badges -->
    </div>
  </div>
</div>

<!-- Alternative: Admin Panel Style -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Contact Submissions</h3>
    <span class="text-sm text-gray-500" id="submissionCount">0 submissions</span>
  </div>
  
  <div id="visitorList" class="space-y-1">
    <!-- Submissions will appear here -->
  </div>
  
  <!-- Clear button -->
  <button 
    onclick="clearSubmissions()" 
    class="mt-4 text-sm text-red-600 hover:text-red-800 dark:text-red-400"
  >
    Clear All
  </button>
</div>

<script>
// Additional helper functions you can add to your main script

// Clear all submissions
function clearSubmissions() {
  if (confirm('Are you sure you want to clear all contact submissions?')) {
    localStorage.removeItem('contactSubmissions');
    updateVisitorDisplay();
    updateSubmissionCount();
  }
}

// Update submission count
function updateSubmissionCount() {
  const countElement = document.getElementById('submissionCount');
  if (countElement) {
    try {
      const submissions = JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
      countElement.textContent = `${submissions.length} submission${submissions.length !== 1 ? 's' : ''}`;
    } catch (error) {
      countElement.textContent = '0 submissions';
    }
  }
}

// Alternative visitor display for badges
function updateVisitorDisplayBadges() {
  const visitorList = document.getElementById('visitorList');
  if (!visitorList) return;
  
  try {
    const submissions = JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
    
    if (submissions.length === 0) {
      visitorList.innerHTML = '<span class="text-gray-500 text-sm">No recent contacts</span>';
      return;
    }
    
    visitorList.innerHTML = submissions.map(submission => `
      <span class="inline-block bg-primary text-white text-xs px-2 py-1 rounded-full">
        ${submission.name}
      </span>
    `).join('');
  } catch (error) {
    console.log('Could not update visitor display:', error);
  }
}

// Get visitor statistics
function getVisitorStats() {
  try {
    const submissions = JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
    return {
      total: submissions.length,
      today: submissions.filter(s => s.date === new Date().toLocaleDateString()).length,
      thisWeek: submissions.filter(s => {
        const submissionDate = new Date(s.timestamp);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return submissionDate > weekAgo;
      }).length
    };
  } catch (error) {
    return { total: 0, today: 0, thisWeek: 0 };
  }
}
</script>
