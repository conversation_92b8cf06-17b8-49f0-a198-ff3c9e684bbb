<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Personal Portfolio</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="icon" href="img/logo.png">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#15B8A6", secondary: "#10b981" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <!-- EmailJS CDN -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: 'Inter', sans-serif;
      }
      .scroll-down-arrow {
      animation: bounce 2s infinite;
      }
      @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
      }
      .scroll-top-button {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
      }
      .scroll-top-button.visible {
      opacity: 1;
      visibility: visible;
      }
      input:focus, button:focus {
      outline: none;
      }
      .animate-fade-up {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.6s ease-out, transform 0.6s ease-out;
      }
      .animate-fade-up.visible {
      opacity: 1;
      transform: translateY(0);
      }
      .animate-fade-in {
      opacity: 0;
      transition: opacity 0.6s ease-out;
      }
      .animate-fade-in.visible {
      opacity: 1;
      }
      .stagger-animation {
      transition-delay: calc(var(--animation-order) * 0.1s);
      }
      .scale-hover {
      transition: transform 0.3s ease;
      }
      .scale-hover:hover {
      transform: scale(1.02);
      }
      /* .nav-link {
      position: relative;
      }
      .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -4px;
      left: 0;
      background-color: #15B8A6;
      transition: width 0.3s ease;
      }
      .nav-link:hover::after {
      width: 100%;
      } */
      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        height: 3px;
        background: #15B8A6;
        z-index: 9999;
        transition: width 0.1s ease;
      }
      @keyframes blink-caret {
      from, to { border-color: transparent }
      50% { border-color: #15B8A6 }
      }

      /* Enhanced Background Animations */
      @keyframes fall {
        0% {
          transform: translateY(-100vh) rotate(0deg);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes slideInLeft {
        0% { transform: translateX(-100px); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
      }

      /* Background Elements */
      .bg-animated {
        position: relative;
        overflow: hidden;
      }

      .bg-animated::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(21, 184, 166, 0.1) 0%,
          rgba(16, 185, 129, 0.05) 25%,
          rgba(59, 130, 246, 0.05) 50%,
          rgba(139, 92, 246, 0.1) 75%,
          rgba(236, 72, 153, 0.05) 100%);
        z-index: 1;
      }

      /* Honeycomb Elements */
      .honeycomb {
        position: absolute;
        width: 40px;
        height: 40px;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.15), rgba(16, 185, 129, 0.1));
        backdrop-filter: blur(8px);
        z-index: 2;
        clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
        animation: fall linear infinite;
      }

      .honeycomb:nth-child(1) {
        left: 10%;
        animation-duration: 8s;
        animation-delay: 0s;
      }

      .honeycomb:nth-child(2) {
        left: 20%;
        animation-duration: 10s;
        animation-delay: -2s;
        background: linear-gradient(45deg, rgba(59, 130, 246, 0.12), rgba(139, 92, 246, 0.08));
      }

      .honeycomb:nth-child(3) {
        left: 30%;
        animation-duration: 12s;
        animation-delay: -4s;
        width: 35px;
        height: 35px;
      }

      .honeycomb:nth-child(4) {
        left: 40%;
        animation-duration: 9s;
        animation-delay: -1s;
        background: linear-gradient(45deg, rgba(236, 72, 153, 0.1), rgba(251, 146, 60, 0.08));
      }

      .honeycomb:nth-child(5) {
        left: 50%;
        animation-duration: 11s;
        animation-delay: -3s;
        width: 45px;
        height: 45px;
      }

      .honeycomb:nth-child(6) {
        left: 60%;
        animation-duration: 7s;
        animation-delay: -5s;
        background: linear-gradient(45deg, rgba(16, 185, 129, 0.12), rgba(21, 184, 166, 0.09));
      }

      .honeycomb:nth-child(7) {
        left: 70%;
        animation-duration: 13s;
        animation-delay: -2.5s;
        width: 38px;
        height: 38px;
      }

      .honeycomb:nth-child(8) {
        left: 80%;
        animation-duration: 8.5s;
        animation-delay: -4.5s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.08));
      }

      .honeycomb:nth-child(9) {
        left: 90%;
        animation-duration: 10.5s;
        animation-delay: -1.5s;
        width: 42px;
        height: 42px;
      }

      .honeycomb:nth-child(10) {
        left: 15%;
        animation-duration: 9.5s;
        animation-delay: -3.5s;
        width: 36px;
        height: 36px;
        background: linear-gradient(45deg, rgba(251, 146, 60, 0.12), rgba(236, 72, 153, 0.1));
      }

      .honeycomb:nth-child(11) {
        left: 25%;
        animation-duration: 11.5s;
        animation-delay: -0.5s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.15), rgba(59, 130, 246, 0.1));
      }

      .honeycomb:nth-child(12) {
        left: 35%;
        animation-duration: 8.2s;
        animation-delay: -2.8s;
        width: 44px;
        height: 44px;
      }

      .honeycomb:nth-child(13) {
        left: 45%;
        animation-duration: 12.5s;
        animation-delay: -4.2s;
        background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(21, 184, 166, 0.12));
        width: 38px;
        height: 38px;
      }

      .honeycomb:nth-child(14) {
        left: 55%;
        animation-duration: 7.8s;
        animation-delay: -1.8s;
        background: linear-gradient(45deg, rgba(236, 72, 153, 0.12), rgba(251, 146, 60, 0.09));
      }

      .honeycomb:nth-child(15) {
        left: 65%;
        animation-duration: 10.8s;
        animation-delay: -3.2s;
        width: 41px;
        height: 41px;
      }

      .honeycomb:nth-child(16) {
        left: 75%;
        animation-duration: 9.2s;
        animation-delay: -5.2s;
        background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.12));
        width: 37px;
        height: 37px;
      }

      .honeycomb:nth-child(17) {
        left: 85%;
        animation-duration: 11.8s;
        animation-delay: -0.8s;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.12), rgba(16, 185, 129, 0.1));
      }

      .honeycomb:nth-child(18) {
        left: 5%;
        animation-duration: 8.8s;
        animation-delay: -4.8s;
        width: 43px;
        height: 43px;
        background: linear-gradient(45deg, rgba(251, 146, 60, 0.1), rgba(236, 72, 153, 0.08));
      }

      .honeycomb:nth-child(19) {
        left: 95%;
        animation-duration: 13.2s;
        animation-delay: -2.2s;
        width: 39px;
        height: 39px;
      }

      .honeycomb:nth-child(20) {
        left: 12%;
        animation-duration: 7.5s;
        animation-delay: -6s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.08), rgba(59, 130, 246, 0.12));
        width: 46px;
        height: 46px;
      }

      /* Glassmorphism Effect */
      .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Enhanced Section Backgrounds */
      .section-pattern {
        background-image:
          radial-gradient(circle at 25% 25%, rgba(21, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
      }

      /* Hexagon Profile Image Styles */
      .hexagon-container {
        width: 400px;
        height: 400px;
        margin: 0 auto;
        position: relative;
        transition: transform 0.3s ease;
      }

      .hexagon-container:hover {
        transform: scale(1.05);
      }

      .hexagon-shape {
        width: 100%;
        height: 100%;
        position: relative;
        clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
        background: linear-gradient(135deg, #15B8A6, #10b981);
        padding: 4px;
        border-radius: 8px;
      }

      .hexagon-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
        transition: transform 0.3s ease;
      }

      .hexagon-border {
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        background: linear-gradient(45deg, #15B8A6, #10b981, #06b6d4, #8b5cf6);
        clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
        animation: borderGlow 3s ease-in-out infinite;
      }

      .hexagon-container:hover .hexagon-border {
        opacity: 1;
      }

      .hexagon-container:hover .hexagon-image {
        transform: scale(1.1);
      }

      @keyframes borderGlow {
        0%, 100% {
          background: linear-gradient(45deg, #15B8A6, #10b981, #06b6d4, #8b5cf6);
        }
        25% {
          background: linear-gradient(45deg, #10b981, #06b6d4, #8b5cf6, #15B8A6);
        }
        50% {
          background: linear-gradient(45deg, #06b6d4, #8b5cf6, #15B8A6, #10b981);
        }
        75% {
          background: linear-gradient(45deg, #8b5cf6, #15B8A6, #10b981, #06b6d4);
        }
      }

      /* Curved Background Shapes */
      .curve-shape {
        position: absolute;
        border-radius: 50% 30% 70% 40%;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.1), rgba(16, 185, 129, 0.15));
        backdrop-filter: blur(20px);
        animation: morphing 8s ease-in-out infinite;
      }

      .curve-shape-2 {
        position: absolute;
        border-radius: 40% 60% 30% 70%;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(139, 92, 246, 0.12));
        backdrop-filter: blur(15px);
        animation: morphing-reverse 10s ease-in-out infinite;
      }

      .curve-shape-3 {
        position: absolute;
        border-radius: 60% 40% 50% 30%;
        background: linear-gradient(225deg, rgba(236, 72, 153, 0.06), rgba(251, 146, 60, 0.1));
        backdrop-filter: blur(25px);
        animation: morphing-slow 12s ease-in-out infinite;
      }

      @keyframes morphing {
        0%, 100% {
          border-radius: 50% 30% 70% 40%;
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          border-radius: 30% 70% 40% 50%;
          transform: translateY(-15px) rotate(90deg);
        }
        50% {
          border-radius: 70% 40% 50% 30%;
          transform: translateY(-30px) rotate(180deg);
        }
        75% {
          border-radius: 40% 50% 30% 70%;
          transform: translateY(-15px) rotate(270deg);
        }
      }

      @keyframes morphing-reverse {
        0%, 100% {
          border-radius: 40% 60% 30% 70%;
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          border-radius: 60% 30% 70% 40%;
          transform: translateY(20px) rotate(-90deg);
        }
        50% {
          border-radius: 30% 70% 40% 60%;
          transform: translateY(40px) rotate(-180deg);
        }
        75% {
          border-radius: 70% 40% 60% 30%;
          transform: translateY(20px) rotate(-270deg);
        }
      }

      @keyframes morphing-slow {
        0%, 100% {
          border-radius: 60% 40% 50% 30%;
          transform: translateX(0px) translateY(0px) rotate(0deg);
        }
        33% {
          border-radius: 40% 50% 30% 60%;
          transform: translateX(15px) translateY(-20px) rotate(120deg);
        }
        66% {
          border-radius: 50% 30% 60% 40%;
          transform: translateX(-15px) translateY(-10px) rotate(240deg);
        }
      }

      /* Dark Mode Styles */
      .dark {
        color-scheme: dark;
      }

      .dark body {
        background-color: #0f172a;
        color: #e2e8f0;
      }

      .dark nav {
        background-color: rgba(15, 23, 42, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(51, 65, 85, 0.3);
      }

      .dark .bg-white {
        background-color: #1e293b;
        border: 1px solid rgba(51, 65, 85, 0.3);
      }

      .dark .bg-gray-50 {
        background-color: #0f172a;
      }

      .dark .text-gray-800 {
        color: #e2e8f0;
      }

      .dark .text-gray-600 {
        color: #94a3b8;
      }

      .dark .text-gray-700 {
        color: #cbd5e1;
      }

      .dark .text-gray-400 {
        color: #64748b;
      }

      .dark .border-gray-300 {
        border-color: #334155;
      }

      .dark .border-gray-700 {
        border-color: #475569;
      }

      .dark .bg-gray-100 {
        background-color: #334155;
      }

      .dark .bg-gray-200 {
        background-color: #475569;
      }

      .dark .bg-blue-100 {
        background-color: rgba(59, 130, 246, 0.1);
      }

      .dark .shadow-md {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
      }

      .dark .shadow-xl {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
      }

      .dark #hero {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%) !important;
      }

      .dark footer {
        background: linear-gradient(to right, #020617, #0f172a, #020617);
      }

      /* Dark Mode Toggle Button */
      .theme-toggle {
        position: relative;
        width: 50px;
        height: 26px;
        background-color: #374151;
        border-radius: 50px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        border: 2px solid #6b7280;
      }

      .dark .theme-toggle {
        background-color: #15B8A6;
        border-color: #15B8A6;
      }

      .theme-toggle::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 18px;
        height: 18px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .dark .theme-toggle::before {
        transform: translateX(24px);
      }

      .theme-toggle-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        transition: opacity 0.3s ease;
      }

      .theme-toggle .sun-icon {
        left: 6px;
        opacity: 1;
        color: #fbbf24;
      }

      .theme-toggle .moon-icon {
        right: 6px;
        opacity: 0;
        color: #e2e8f0;
      }

      .dark .theme-toggle .sun-icon {
        opacity: 0;
      }

      .dark .theme-toggle .moon-icon {
        opacity: 1;
      }

      /* Smooth transitions for theme switching */
      * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
      }

      /* Enhanced Contact Form Styles */
      .form-group {
        position: relative;
      }

      .form-input {
        transition: all 0.3s ease;
      }

      .form-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(21, 184, 166, 0.15);
      }

      .form-input.error {
        border-color: #ef4444;
        background-color: rgba(239, 68, 68, 0.05);
      }

      .form-input.success {
        border-color: #10b981;
        background-color: rgba(16, 185, 129, 0.05);
      }

      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
      }

      .error-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .success-message {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.5s ease;
      }

      .success-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .error-notification {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.5s ease;
      }

      .error-notification.show {
        opacity: 1;
        transform: translateY(0);
      }

      .character-counter {
        font-size: 0.75rem;
        color: #6b7280;
        text-align: right;
        margin-top: 0.25rem;
        transition: color 0.3s ease;
      }

      .character-counter.warning {
        color: #f59e0b;
      }

      .character-counter.error {
        color: #ef4444;
      }

      .submit-button {
        position: relative;
        overflow: hidden;
      }

      .submit-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .submit-button .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .submit-button.loading .loading-spinner {
        display: inline-block;
      }

      .submit-button.loading .button-text {
        display: none;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Dark mode form styles */
      .dark .form-input.error {
        background-color: rgba(239, 68, 68, 0.1);
      }

      .dark .form-input.success {
        background-color: rgba(16, 185, 129, 0.1);
      }

      .dark .character-counter {
        color: #9ca3af;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 w-full bg-white shadow-md z-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <a href="#" class="text-2xl font-['Pacifico']" style="color: #15B8A6;"
            >iArcillas</a
          >
          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center space-x-8">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <!-- Dark Mode Toggle -->
            <div class="theme-toggle" id="themeToggle">
              <i class="ri-sun-line theme-toggle-icon sun-icon"></i>
              <i class="ri-moon-line theme-toggle-icon moon-icon"></i>
            </div>
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300"
              >Contact</a
            >
          </div>
          <!-- Mobile Menu Button -->
          <button
            id="mobileMenuBtn"
            class="md:hidden w-10 h-10 flex items-center justify-center text-gray-600"
          >
            <i class="ri-menu-line ri-lg"></i>
          </button>
        </div>
        <!-- Mobile Navigation -->
        <div id="mobileMenu" class="hidden md:hidden py-4">
          <div class="flex flex-col space-y-4">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <!-- Dark Mode Toggle for Mobile -->
            <div class="flex items-center justify-between py-2">
              <span class="text-gray-600">Dark Mode</span>
              <div class="theme-toggle" id="mobileThemeToggle">
                <i class="ri-sun-line theme-toggle-icon sun-icon"></i>
                <i class="ri-moon-line theme-toggle-icon moon-icon"></i>
              </div>
            </div>
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300 inline-block text-center"
              >Contact</a
            >
          </div>
        </div>
      </div>
    </nav>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>
    <!-- Hero Section -->
    <section
      id="hero"
      class="h-screen flex items-center justify-center relative bg-animated pt-16 overflow-hidden"
      style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);"
    >
      <!-- Falling Honeycomb Elements -->
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>

      <div class="container hero mx-auto px-6 md:px-12 text-center relative z-10">
        <h2 class="text-2xl md:text-3xl text-gray-600 mb-4">Hi, I'm</h2>
        <h1
          class="text-5xl md:text-7xl  font-bold text-gray-800 mb-6 mx-auto"
        >
          Ivan Jay Arcillas
        </h1>
        <p class="text-xl md:text-2xl text-gray-600 mt-4 mb-8">
          Frontend Developer & UI/UX Designer
        </p>
        <div class="flex justify-center gap-4 mt-8">
          <a
            href="#projects"
            class="bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            View Portfolio
          </a>
          <a
            href="#contact"
            class="bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            Contact Me
          </a>
        </div>
      </div>
</section>
    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            About Me
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
        </div>
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="md:w-1/2 flex justify-center">
            <div class="hexagon-container relative">
              <div class="hexagon-shape">
                <img
                  src="img/profile2.png"
                  alt="John Anderson"
                  class="hexagon-image"
                />
              </div>
              <div class="hexagon-border"></div>
            </div>
          </div>
          <div class="md:w-1/2">
            <h3 class="text-2xl font-semibold text-gray-800 mb-4">Who I Am</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              I'm a passionate frontend developer and UI/UX designer with over 5
              years of experience creating beautiful, functional, and
              user-centered digital experiences. Based in San Francisco, I
              combine technical expertise with creative problem-solving to build
              websites and applications that people love to use.
            </p>
            <p class="text-gray-600 mb-8 leading-relaxed">
              My approach to design focuses on the perfect balance of form and
              function. I believe that great design should not only look good
              but also solve real problems and create meaningful connections
              with users.
            </p>
          </div>
        </div>
      </div>
    </section>
    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Projects
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Here are some of my recent projects that showcase my skills and
            expertise in frontend development and UI/UX design.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Project 1 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <img
                src="https://readdy.ai/api/search-image?query=modern%20e-commerce%20website%20interface%20with%20clean%20design%2C%20product%20grid%20layout%2C%20shopping%20cart%20functionality%2C%20responsive%20design%20elements%2C%20professional%20UI%2FUX%20design%2C%20high%20quality%20digital%20mockup&width=600&height=400&seq=portfolio2&orientation=landscape"
                alt="E-commerce Website"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                E-commerce Website
              </h3>
              <p class="text-gray-600 mb-4">
                A fully responsive e-commerce platform with cart functionality,
                user authentication, and payment integration.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >React</span
                >
                <span
                  class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                  >Node.js</span
                >
                <span
                  class="text-xs font-medium bg-purple-100 text-purple-800 px-2.5 py-0.5 rounded"
                  >MongoDB</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
          <!-- Project 2 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <a href="project/analytics_dashboard.html">
                <img
                src="https://readdy.ai/api/search-image?query=mobile%20app%20dashboard%20interface%20with%20data%20visualization%2C%20user%20analytics%2C%20modern%20UI%20design%2C%20dark%20mode%2C%20charts%20and%20graphs%2C%20professional%20mobile%20application%20mockup&width=600&height=400&seq=portfolio3&orientation=landscape"
                alt="Dashboard App"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
              </a>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                Analytics Dashboard
              </h3>
              <p class="text-gray-600 mb-4">
                An interactive dashboard for visualizing business metrics with
                real-time data updates and customizable widgets.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >Vue.js</span
                >
                <span
                  class="text-xs font-medium bg-yellow-100 text-yellow-800 px-2.5 py-0.5 rounded"
                  >D3.js</span
                >
                <span
                  class="text-xs font-medium bg-red-100 text-red-800 px-2.5 py-0.5 rounded"
                  >Firebase</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
          <!-- Project 3 -->
          <div
            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card"
          >
            <div class="h-60 overflow-hidden">
              <img
                src="https://readdy.ai/api/search-image?query=fitness%20mobile%20application%20interface%20with%20workout%20tracking%2C%20progress%20charts%2C%20clean%20minimal%20design%2C%20user%20profile%20page%2C%20exercise%20library%2C%20professional%20mobile%20app%20mockup&width=600&height=400&seq=portfolio4&orientation=landscape"
                alt="Fitness App"
                class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-800 mb-2">
                Fitness Tracker App
              </h3>
              <p class="text-gray-600 mb-4">
                A mobile app for tracking workouts, setting fitness goals, and
                monitoring progress with personalized recommendations.
              </p>
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                  >React Native</span
                >
                <span
                  class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                  >Redux</span
                >
                <span
                  class="text-xs font-medium bg-gray-100 text-gray-800 px-2.5 py-0.5 rounded"
                  >GraphQL</span
                >
              </div>
              <a
                href="#"
                class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
              >
                View Project
                <div class="w-5 h-5 ml-1 flex items-center justify-center">
                  <i class="ri-arrow-right-line"></i>
                </div>
              </a>
            </div>
          </div>
        </div>
        <div class="text-center mt-12">
          <a
            href="#"
            class="inline-block bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
          >
            View All Projects
          </a>
        </div>
      </div>
    </section>
    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Skills
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            I've developed expertise in various technologies and tools
            throughout my career.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- Technical Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Technical Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6 skill-card">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >HTML & CSS</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full animate-width"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >JavaScript</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">React</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Vue.js</span>
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Node.js</span>
                <span class="text-sm font-medium text-gray-700">75%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 75%"
                ></div>
              </div>
            </div>
          </div>
          <!-- Design Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Design Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >UI/UX Design</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Figma</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Adobe XD</span
                >
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Responsive Design</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 95%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Prototyping</span
                >
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Get In Touch
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Have a project in mind or want to collaborate? Feel free to reach
            out to me.
          </p>
        </div>
        <div class="flex flex-col md:flex-row gap-12">
          <!-- Contact Form -->
          <div class="md:w-2/3 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Send Me a Message
            </h3>

            <!-- Success Message -->
            <div id="successMessage" class="success-message">
              <div class="flex items-center">
                <i class="ri-check-circle-line ri-lg mr-3"></i>
                <div>
                  <h4 class="font-semibold">Message Sent Successfully!</h4>
                  <p class="text-sm opacity-90">Thank you for reaching out. I'll get back to you soon.</p>
                </div>
              </div>
            </div>

            <form id="contactForm" novalidate>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="form-group">
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Name <span class="text-red-500">*</span></label
                  >
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    minlength="2"
                    maxlength="50"
                    class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="John Doe"
                  />
                  <div class="error-message" id="nameError"></div>
                </div>
                <div class="form-group">
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Email <span class="text-red-500">*</span></label
                  >
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="<EMAIL>"
                  />
                  <div class="error-message" id="emailError"></div>
                </div>
              </div>
              <div class="form-group mb-6">
                <label
                  for="subject"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Subject <span class="text-red-500">*</span></label
                >
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  required
                  minlength="5"
                  maxlength="100"
                  class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Project Inquiry"
                />
                <div class="error-message" id="subjectError"></div>
              </div>
              <div class="form-group mb-6">
                <label
                  for="message"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Message <span class="text-red-500">*</span></label
                >
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  required
                  minlength="10"
                  maxlength="1000"
                  class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Your message here..."
                ></textarea>
                <div class="character-counter" id="messageCounter">0 / 1000 characters</div>
                <div class="error-message" id="messageError"></div>
              </div>
              <button
                type="submit"
                id="submitButton"
                class="submit-button bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
              >
                <span class="loading-spinner"></span>
                <span class="button-text">Send Message</span>
              </button>
            </form>
          </div>
          <!-- Contact Info -->
          <div class="md:w-1/3">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Contact Information
              </h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-map-pin-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Location</h4>
                    <p class="text-gray-600">Sagay City Negros Occidental</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-mail-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Email</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-phone-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Phone</h4>
                    <p class="text-gray-600">+****************</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Follow Me
              </h3>
              <div class="flex space-x-4">
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-github-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-linkedin-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-twitter-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-dribbble-fill ri-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 text-white py-12 relative overflow-hidden">
      <!-- Scroll to Top Button -->
      <button
        id="scrollTopBtn"
        class="fixed bottom-8 right-8 w-10 h-10 bg-primary hover:bg-[#13a696] text-white rounded-full shadow-xl flex items-center justify-center transition-all duration-300 z-50 opacity-100 visible hover:scale-110"
      >
        <i class="ri-arrow-up-s-line ri-xl"></i>
      </button>

      <div class="container mx-auto px-6 md:px-12">
        <div class="flex flex-col md:flex-row justify-between items-left">
          <div class="mb-6 md:mb-0">
            <a href="#" class="text-2xl font-['Pacifico'] text-white"
              >iArcillas</a
            >
            <p class="text-gray-400 mt-2">
              Frontend Developer & UI/UX Designer
            </p>
          </div>
          <div
            class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-8"
          >
            <a
              href="#hero"
              class="text-gray-300 hover:text-white transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-300 hover:text-white transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-300 hover:text-white transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-300 hover:text-white transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="text-gray-300 hover:text-white transition-colors"
              >Contact</a
            >
          </div>
        </div>
        <hr class="border-gray-700 my-8" />
        <div class="text-center text-gray-400">
          <p>© 2025 iArcillas. All rights reserved.</p>
          <p class="mt-2">Last updated: May 29, 2025</p>
        </div>
      </div>
    </footer>
    <!-- Scripts -->
    <script id="navigationBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");
        const mobileMenu = document.getElementById("mobileMenu");
        const mobileMenuIcon = mobileMenuBtn.querySelector("i");
        // Toggle mobile menu
        mobileMenuBtn.addEventListener("click", function () {
          mobileMenu.classList.toggle("hidden");
          const isOpen = !mobileMenu.classList.contains("hidden");
          mobileMenuIcon.className = isOpen
            ? "ri-close-line ri-lg"
            : "ri-menu-line ri-lg";
        });
        // Close mobile menu when clicking menu items
        mobileMenu.querySelectorAll("a").forEach((link) => {
          link.addEventListener("click", function () {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          });
        });
        // Close mobile menu on window resize
        window.addEventListener("resize", function () {
          if (window.innerWidth >= 768) {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          }
        });

        // Close mobile menu when clicking outside
        document.addEventListener("click", function (event) {
          const nav = document.querySelector("nav");
          const isClickInsideNav = nav.contains(event.target);
          const isMenuOpen = !mobileMenu.classList.contains("hidden");

          if (!isClickInsideNav && isMenuOpen) {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          }
        });
      });
    </script>
    <script id="scrollBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const targetId = this.getAttribute("href");
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
              window.scrollTo({
                top: targetElement.offsetTop,
                behavior: "smooth",
              });
            }
          });
        });
        // Intersection Observer for scroll animations
        const animateOnScroll = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                entry.target.classList.add("visible");
              }
            });
          },
          {
            threshold: 0.1,
          },
        );
        // Add animation classes and observe elements
        document.querySelectorAll("section").forEach((section) => {
          section.classList.add("animate-fade-up");
          animateOnScroll.observe(section);
        });
        document
          .querySelectorAll(".project-card, .skill-card")
          .forEach((element, index) => {
            element.classList.add("animate-fade-up", "stagger-animation");
            element.style.setProperty("--animation-order", index);
            animateOnScroll.observe(element);
          });
        document.querySelectorAll("img").forEach((img) => {
          img.classList.add("animate-fade-in");
          animateOnScroll.observe(img);
        });
        // Add hover animations to cards and buttons
        document.querySelectorAll(".bg-white.rounded-lg").forEach((card) => {
          card.classList.add("scale-hover");
        });
        // Add nav link hover effect
        document.querySelectorAll(".text-gray-600").forEach((link) => {
          if (link.tagName === "A") {
            link.classList.add("nav-link");
          }
        });
      });
    </script>
    <script id="scrollToTopButton">
      document.addEventListener("DOMContentLoaded", function () {
        const scrollTopBtn = document.getElementById("scrollTopBtn");

        // Initially hide the button
        scrollTopBtn.style.display = "none";

        // Show/hide button based on scroll position
        window.addEventListener("scroll", function() {
          if (window.scrollY > 2000) {
            scrollTopBtn.style.display = "flex";
          } else {
            scrollTopBtn.style.display = "none";
          }
        });

        // Scroll to top when button is clicked
        scrollTopBtn.addEventListener("click", function () {
          window.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        });
      });
    </script>
    <script id="progressBarScript">
      document.addEventListener("DOMContentLoaded", function() {
        const progressBar = document.getElementById("progressBar");

        window.addEventListener("scroll", function() {
          const windowHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
          const scrolled = (window.scrollY / windowHeight) * 100;
          progressBar.style.width = scrolled + "%";
        });


         // Add typing effect to hero title (optional enhancement)
        const heroTitle = document.querySelector('.hero h1');
        if (heroTitle) {
            const text = heroTitle.textContent;
            heroTitle.textContent = '';
            let i = 0;

            const typeWriter = () => {
                if (i < text.length) {
                    heroTitle.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };

            // Start typing effect after page load
            setTimeout(typeWriter, 500);
        }

      });
    </script>
    <script id="parallaxEffect">
      document.addEventListener("DOMContentLoaded", function() {
        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('#hero');
            const heroContent = document.querySelector('#hero .container');

            if (hero && heroContent) {
                heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
      });
    </script>
    <script id="darkModeToggle">
      document.addEventListener("DOMContentLoaded", function() {
        const themeToggle = document.getElementById('themeToggle');
        const mobileThemeToggle = document.getElementById('mobileThemeToggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // Set initial theme
        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
          html.classList.add('dark');
        }

        // Toggle theme function
        function toggleTheme() {
          html.classList.toggle('dark');
          const isDark = html.classList.contains('dark');
          localStorage.setItem('theme', isDark ? 'dark' : 'light');
        }

        // Add event listeners to both toggles
        if (themeToggle) {
          themeToggle.addEventListener('click', toggleTheme);
        }

        if (mobileThemeToggle) {
          mobileThemeToggle.addEventListener('click', toggleTheme);
        }

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          if (!localStorage.getItem('theme')) {
            if (e.matches) {
              html.classList.add('dark');
            } else {
              html.classList.remove('dark');
            }
          }
        });
      });
    </script>
    <script id="enhancedContactForm">
      document.addEventListener("DOMContentLoaded", function() {
        // EmailJS Configuration
        const EMAILJS_CONFIG = {
          publicKey: '3MjPixLe0W6PUT5Vt', // Replace with your EmailJS public key
          serviceId: 'service_wxcd4rn', // Replace with your EmailJS service ID
          templateId: 'template_lyeivof' // Replace with your EmailJS template ID
        };

        // Initialize EmailJS
        emailjs.init(EMAILJS_CONFIG.publicKey);

        const form = document.getElementById('contactForm');
        const submitButton = document.getElementById('submitButton');
        const successMessage = document.getElementById('successMessage');
        const messageTextarea = document.getElementById('message');
        const messageCounter = document.getElementById('messageCounter');

        // Form validation rules
        const validationRules = {
          name: {
            required: true,
            minLength: 2,
            maxLength: 50,
            pattern: /^[a-zA-Z\s]+$/,
            message: 'Name must be 2-50 characters and contain only letters and spaces'
          },
          email: {
            required: true,
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address'
          },
          subject: {
            required: true,
            minLength: 5,
            maxLength: 100,
            message: 'Subject must be 5-100 characters long'
          },
          message: {
            required: true,
            minLength: 10,
            maxLength: 1000,
            message: 'Message must be 10-1000 characters long'
          }
        };

        // Update character counter
        function updateCharacterCounter() {
          const length = messageTextarea.value.length;
          const maxLength = 1000;
          messageCounter.textContent = `${length} / ${maxLength} characters`;

          // Update counter color based on length
          messageCounter.classList.remove('warning', 'error');
          if (length > maxLength * 0.9) {
            messageCounter.classList.add('warning');
          }
          if (length > maxLength) {
            messageCounter.classList.add('error');
          }
        }

        // Validate individual field
        function validateField(fieldName, value) {
          const rules = validationRules[fieldName];
          const errors = [];

          if (rules.required && !value.trim()) {
            errors.push(`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
          }

          if (value.trim()) {
            if (rules.minLength && value.length < rules.minLength) {
              errors.push(`Minimum ${rules.minLength} characters required`);
            }

            if (rules.maxLength && value.length > rules.maxLength) {
              errors.push(`Maximum ${rules.maxLength} characters allowed`);
            }

            if (rules.pattern && !rules.pattern.test(value)) {
              errors.push(rules.message);
            }
          }

          return errors;
        }

        // Show field error
        function showFieldError(fieldName, errors) {
          const field = document.getElementById(fieldName);
          const errorElement = document.getElementById(fieldName + 'Error');

          field.classList.remove('success', 'error');
          errorElement.classList.remove('show');

          if (errors.length > 0) {
            field.classList.add('error');
            errorElement.textContent = errors[0];
            errorElement.classList.add('show');
            return false;
          } else if (field.value.trim()) {
            field.classList.add('success');
          }

          return true;
        }

        // Real-time validation
        function setupRealTimeValidation() {
          Object.keys(validationRules).forEach(fieldName => {
            const field = document.getElementById(fieldName);

            field.addEventListener('blur', function() {
              const errors = validateField(fieldName, this.value);
              showFieldError(fieldName, errors);
            });

            field.addEventListener('input', function() {
              // Clear error state on input
              if (this.classList.contains('error')) {
                const errors = validateField(fieldName, this.value);
                if (errors.length === 0) {
                  showFieldError(fieldName, []);
                }
              }
            });
          });
        }

        // Validate entire form
        function validateForm() {
          let isValid = true;
          const formData = new FormData(form);

          Object.keys(validationRules).forEach(fieldName => {
            const value = formData.get(fieldName) || '';
            const errors = validateField(fieldName, value);
            const fieldValid = showFieldError(fieldName, errors);
            if (!fieldValid) isValid = false;
          });

          return isValid;
        }

        // Submit form using EmailJS
        async function submitForm(formData) {
          try {
            // Prepare template parameters for EmailJS
            const templateParams = {
              from_name: formData.get('name'),
              from_email: formData.get('email'),
              subject: formData.get('subject'),
              message: formData.get('message'),
              to_name: 'Ivan Jay Arcillas', // Your name
              reply_to: formData.get('email')
            };

            // Send email using EmailJS
            const response = await emailjs.send(
              EMAILJS_CONFIG.serviceId,
              EMAILJS_CONFIG.templateId,
              templateParams
            );

            console.log('Email sent successfully:', response);
            return { success: true, response };

          } catch (error) {
            console.error('EmailJS error:', error);
            throw new Error('Failed to send email: ' + error.text || error.message);
          }
        }

        // Handle form submission
        form.addEventListener('submit', async function(e) {
          e.preventDefault();

          if (!validateForm()) {
            return;
          }

          // Show loading state
          submitButton.classList.add('loading');
          submitButton.disabled = true;

          try {
            const formData = new FormData(form);
            const result = await submitForm(formData);

            if (result.success) {
              // Show success message
              successMessage.classList.add('show');
              form.reset();
              updateCharacterCounter();

              // Clear all field states
              document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('success', 'error');
              });

              // Hide success message after 5 seconds
              setTimeout(() => {
                successMessage.classList.remove('show');
              }, 5000);
            }
          } catch (error) {
            console.error('Form submission error:', error);

            // Show user-friendly error message
            let errorMessage = 'There was an error sending your message. Please try again.';

            if (error.message.includes('Failed to send email')) {
              errorMessage = 'Unable to send email. Please check your internet connection and try again.';
            } else if (error.message.includes('Invalid email')) {
              errorMessage = 'Please check your email address and try again.';
            }

            // Create and show error notification
            showErrorMessage(errorMessage);
          } finally {
            // Remove loading state
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
          }
        });

        // Show error message
        function showErrorMessage(message) {
          // Remove existing error message if any
          const existingError = document.querySelector('.error-notification');
          if (existingError) {
            existingError.remove();
          }

          // Create error notification
          const errorDiv = document.createElement('div');
          errorDiv.className = 'error-notification';
          errorDiv.innerHTML = `
            <div class="flex items-center">
              <i class="ri-error-warning-line ri-lg mr-3"></i>
              <div>
                <h4 class="font-semibold">Error</h4>
                <p class="text-sm opacity-90">${message}</p>
              </div>
            </div>
          `;

          // Insert before the form
          form.parentNode.insertBefore(errorDiv, form);

          // Show with animation
          setTimeout(() => errorDiv.classList.add('show'), 100);

          // Auto-hide after 5 seconds
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => errorDiv.remove(), 300);
          }, 5000);
        }

        // Initialize
        setupRealTimeValidation();
        messageTextarea.addEventListener('input', updateCharacterCounter);
        updateCharacterCounter();
      });
    </script>

  </body>
</html>
