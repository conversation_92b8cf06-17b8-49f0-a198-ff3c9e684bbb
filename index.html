<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Personal Portfolio</title>
    <link rel="icon" href="img/logo.png">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Monserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"/>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#15B8A6", secondary: "#10b981" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <script type="text/javascript">
      (function(){
          emailjs.init({
            publicKey: "3MjPixLe0W6PUT5Vt",
          });
      })();
    </script>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 w-full bg-white shadow-md z-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <a href="#" class="logo-container flex items-center">
            <div class="logo-ia">
              <span class="logo-i">I</span>
              <span class="logo-a">A</span>
            </div>
          </a>
          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center space-x-8">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <!-- Dark Mode Toggle -->
            <div class="theme-toggle" id="themeToggle">
              <i class="ri-sun-line theme-toggle-icon sun-icon"></i>
              <i class="ri-moon-line theme-toggle-icon moon-icon"></i>
            </div>
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300"
              >Contact</a
            >
          </div>
          <!-- Mobile Menu Button -->
          <button
            id="mobileMenuBtn"
            class="md:hidden w-10 h-10 flex items-center justify-center text-gray-600"
          >
            <i class="ri-menu-line ri-lg"></i>
          </button>
        </div>
        <!-- Mobile Navigation -->
        <div id="mobileMenu" class="hidden md:hidden py-4">
          <div class="flex flex-col space-y-4">
            <a
              href="#hero"
              class="text-gray-600 hover:text-primary transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-600 hover:text-primary transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-600 hover:text-primary transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-600 hover:text-primary transition-colors"
              >Skills</a
            >
            <!-- Dark Mode Toggle for Mobile -->
            <div class="flex items-center justify-between py-2">
              <span class="text-gray-600">Dark Mode</span>
              <div class="theme-toggle" id="mobileThemeToggle">
                <i class="ri-sun-line theme-toggle-icon sun-icon"></i>
                <i class="ri-moon-line theme-toggle-icon moon-icon"></i>
              </div>
            </div>
            <a
              href="#contact"
              class="bg-primary hover:bg-[#13a696] text-white px-4 py-2 !rounded-button transition-all duration-300 inline-block text-center"
              >Contact</a
            >
          </div>
        </div>
      </div>
    </nav>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>
    <!-- Hero Section -->
    <section
      id="hero"
      class="h-screen flex items-center justify-center relative bg-animated pt-16 overflow-hidden"
      style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);"
    >
      <!-- Falling Honeycomb Elements -->
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>
      <div class="honeycomb"></div>

      <div class="container hero mx-auto px-6 md:px-12 text-center relative z-10">
        <h2 class="text-2xl md:text-3xl text-gray-600 mb-4">Hi, I'm</h2>
        <h1
          class="text-5xl md:text-7xl  font-bold text-gray-800 mb-6 mx-auto"
        >
          Ivan Jay Arcillas
        </h1>
        <p class="text-xl md:text-2xl text-gray-600 mt-4 mb-8">
          Frontend Developer & UI/UX Designer
        </p>
        <div class="flex justify-center gap-4 mt-8">
          <a
            href="#projects"
            class="bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            View Portfolio
          </a>
          <a
            href="#contact"
            class="bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap inline-block"
          >
            Contact Me
          </a>
        </div>
      </div>
</section>
    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            About Me
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
        </div>
        <div class="flex flex-col md:flex-row gap-12 items-center">
          <div class="md:w-1/2 flex justify-center">
            <div class="hexagon-container relative">
              <div class="hexagon-shape">
                <img
                  src="img/profile3.png"
                  alt="John Anderson"
                  class="hexagon-image"
                />
              </div>
              <div class="hexagon-border"></div>
            </div>
          </div>
          <div class="md:w-1/2">
            <h3 class="text-2xl font-semibold text-gray-800 mb-4">Who I Am</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              I'm a passionate frontend developer and UI/UX designer with over 5
              years of experience creating beautiful, functional, and
              user-centered digital experiences. Based in San Francisco, I
              combine technical expertise with creative problem-solving to build
              websites and applications that people love to use.
            </p>
            <p class="text-gray-600 mb-8 leading-relaxed">
              My approach to design focuses on the perfect balance of form and
              function. I believe that great design should not only look good
              but also solve real problems and create meaningful connections
              with users.
            </p>
          </div>
        </div>
      </div>
    </section>
    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Projects
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Here are some of my recent projects that showcase my skills and
            expertise in frontend development and UI/UX design.
          </p>
        </div>
        <!-- Swiper Container -->
        <div class="swiper projects-swiper">
          <div class="swiper-wrapper">
            <!-- Project 1 -->
            <div class="swiper-slide">
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card w-full"
              >
                <div class="h-60 overflow-hidden">
                  <img
                    src="https://readdy.ai/api/search-image?query=modern%20e-commerce%20website%20interface%20with%20clean%20design%2C%20product%20grid%20layout%2C%20shopping%20cart%20functionality%2C%20responsive%20design%20elements%2C%20professional%20UI%2FUX%20design%2C%20high%20quality%20digital%20mockup&width=600&height=400&seq=portfolio2&orientation=landscape"
                    alt="E-commerce Website"
                    class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
                  />
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-semibold text-gray-800 mb-2">
                    E-commerce Website
                  </h3>
                  <p class="text-gray-600 mb-4">
                    A fully responsive e-commerce platform with cart functionality,
                    user authentication, and payment integration.
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span
                      class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                      >React</span
                    >
                    <span
                      class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                      >Node.js</span
                    >
                    <span
                      class="text-xs font-medium bg-purple-100 text-purple-800 px-2.5 py-0.5 rounded"
                      >MongoDB</span
                    >
                  </div>
                  <a
                    href="#"
                    class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
                  >
                    View Project
                    <div class="w-5 h-5 ml-1 flex items-center justify-center">
                      <i class="ri-arrow-right-line"></i>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <!-- Project 2 -->
            <div class="swiper-slide">
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card w-full"
              >
                <div class="h-60 overflow-hidden">
                  <a href="project/analytics_dashboard.html">
                    <img
                    src="https://readdy.ai/api/search-image?query=mobile%20app%20dashboard%20interface%20with%20data%20visualization%2C%20user%20analytics%2C%20modern%20UI%20design%2C%20dark%20mode%2C%20charts%20and%20graphs%2C%20professional%20mobile%20application%20mockup&width=600&height=400&seq=portfolio3&orientation=landscape"
                    alt="Dashboard App"
                    class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
                  />
                  </a>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-semibold text-gray-800 mb-2">
                    Analytics Dashboard
                  </h3>
                  <p class="text-gray-600 mb-4">
                    An interactive dashboard for visualizing business metrics with
                    real-time data updates and customizable widgets.
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span
                      class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                      >Vue.js</span
                    >
                    <span
                      class="text-xs font-medium bg-yellow-100 text-yellow-800 px-2.5 py-0.5 rounded"
                      >D3.js</span
                    >
                    <span
                      class="text-xs font-medium bg-red-100 text-red-800 px-2.5 py-0.5 rounded"
                      >Firebase</span
                    >
                  </div>
                  <a
                    href="#"
                    class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
                  >
                    View Project
                    <div class="w-5 h-5 ml-1 flex items-center justify-center">
                      <i class="ri-arrow-right-line"></i>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <!-- Project 3 -->
            <div class="swiper-slide">
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card w-full"
              >
                <div class="h-60 overflow-hidden">
                  <img
                    src="https://readdy.ai/api/search-image?query=fitness%20mobile%20application%20interface%20with%20workout%20tracking%2C%20progress%20charts%2C%20clean%20minimal%20design%2C%20user%20profile%20page%2C%20exercise%20library%2C%20professional%20mobile%20app%20mockup&width=600&height=400&seq=portfolio4&orientation=landscape"
                    alt="Fitness App"
                    class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
                  />
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-semibold text-gray-800 mb-2">
                    Fitness Tracker App
                  </h3>
                  <p class="text-gray-600 mb-4">
                    A mobile app for tracking workouts, setting fitness goals, and
                    monitoring progress with personalized recommendations.
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span
                      class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                      >React Native</span
                    >
                    <span
                      class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                      >Redux</span
                    >
                    <span
                      class="text-xs font-medium bg-gray-100 text-gray-800 px-2.5 py-0.5 rounded"
                      >GraphQL</span
                    >
                  </div>
                  <a
                    href="#"
                    class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
                  >
                    View Project
                    <div class="w-5 h-5 ml-1 flex items-center justify-center">
                      <i class="ri-arrow-right-line"></i>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <!-- Project 4 - Additional project -->
            <div class="swiper-slide">
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card w-full"
              >
                <div class="h-60 overflow-hidden">
                  <img
                    src="https://readdy.ai/api/search-image?query=social%20media%20application%20interface%20with%20modern%20design%2C%20user%20profiles%2C%20news%20feed%2C%20messaging%20system%2C%20clean%20UI%2FUX%20design%2C%20professional%20mobile%20app%20mockup&width=600&height=400&seq=portfolio5&orientation=landscape"
                    alt="Social Media App"
                    class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
                  />
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-semibold text-gray-800 mb-2">
                    Social Media Platform
                  </h3>
                  <p class="text-gray-600 mb-4">
                    A modern social networking platform with real-time messaging,
                    content sharing, and advanced user interaction features.
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span
                      class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                      >Next.js</span
                    >
                    <span
                      class="text-xs font-medium bg-purple-100 text-purple-800 px-2.5 py-0.5 rounded"
                      >Socket.io</span
                    >
                    <span
                      class="text-xs font-medium bg-green-100 text-green-800 px-2.5 py-0.5 rounded"
                      >PostgreSQL</span
                    >
                  </div>
                  <a
                    href="#"
                    class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
                  >
                    View Project
                    <div class="w-5 h-5 ml-1 flex items-center justify-center">
                      <i class="ri-arrow-right-line"></i>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <!-- Project 5 - Additional project -->
            <div class="swiper-slide">
              <div
                class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 project-card w-full"
              >
                <div class="h-60 overflow-hidden">
                  <img
                    src="https://readdy.ai/api/search-image?query=task%20management%20application%20interface%20with%20kanban%20board%2C%20project%20tracking%2C%20team%20collaboration%2C%20modern%20productivity%20app%20design%2C%20professional%20UI%2FUX&width=600&height=400&seq=portfolio6&orientation=landscape"
                    alt="Task Management App"
                    class="w-full h-full object-cover object-top transition-transform duration-500 hover:scale-105"
                  />
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-semibold text-gray-800 mb-2">
                    Task Management System
                  </h3>
                  <p class="text-gray-600 mb-4">
                    A comprehensive project management tool with Kanban boards,
                    team collaboration, and advanced productivity features.
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    <span
                      class="text-xs font-medium bg-blue-100 text-blue-800 px-2.5 py-0.5 rounded"
                      >Angular</span
                    >
                    <span
                      class="text-xs font-medium bg-red-100 text-red-800 px-2.5 py-0.5 rounded"
                      >NestJS</span
                    >
                    <span
                      class="text-xs font-medium bg-yellow-100 text-yellow-800 px-2.5 py-0.5 rounded"
                      >MySQL</span
                    >
                  </div>
                  <a
                    href="#"
                    class="inline-flex items-center text-primary hover:text-blue-700 font-medium"
                  >
                    View Project
                    <div class="w-5 h-5 ml-1 flex items-center justify-center">
                      <i class="ri-arrow-right-line"></i>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Navigation buttons -->
          <div class="swiper-button-next"></div>
          <div class="swiper-button-prev"></div>

          <!-- Pagination -->
          <div class="swiper-pagination"></div>
        </div>
        <div class="text-center mt-12">
          <a
            href="#"
            class="inline-block bg-white hover:bg-gray-100 text-gray-800 font-medium py-3 px-8 border border-gray-300 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
          >
            View All Projects
          </a>
        </div>
      </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-white">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            My Skills
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            I've developed expertise in various technologies and tools
            throughout my career.
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- Technical Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Technical Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6 skill-card">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >HTML & CSS</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full animate-width"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >JavaScript</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">React</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Vue.js</span>
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Node.js</span>
                <span class="text-sm font-medium text-gray-700">75%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-primary h-2.5 rounded-full"
                  style="width: 75%"
                ></div>
              </div>
            </div>
          </div>
          <!-- Design Skills -->
          <div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Design Skills
            </h3>
            <!-- Skill 1 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >UI/UX Design</span
                >
                <span class="text-sm font-medium text-gray-700">90%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 90%"
                ></div>
              </div>
            </div>
            <!-- Skill 2 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700">Figma</span>
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
            <!-- Skill 3 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Adobe XD</span
                >
                <span class="text-sm font-medium text-gray-700">80%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 80%"
                ></div>
              </div>
            </div>
            <!-- Skill 4 -->
            <div class="mb-6">
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Responsive Design</span
                >
                <span class="text-sm font-medium text-gray-700">95%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 95%"
                ></div>
              </div>
            </div>
            <!-- Skill 5 -->
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-base font-medium text-gray-700"
                  >Prototyping</span
                >
                <span class="text-sm font-medium text-gray-700">85%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-secondary h-2.5 rounded-full"
                  style="width: 85%"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6 md:px-12">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            Get In Touch
          </h2>
          <div class="w-20 h-1 bg-primary mx-auto"></div>
          <p class="text-gray-600 mt-4 max-w-2xl mx-auto">
            Have a project in mind or want to collaborate? Feel free to reach
            out to me.
          </p>
        </div>
        <div class="flex flex-col md:flex-row gap-12">
          <!-- Contact Form -->
          <div class="md:w-2/3 bg-white rounded-lg shadow-md p-8">
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">
              Send Me a Message
            </h3>

            <!-- Success Message -->
            <div id="successMessage" class="success-message">
              <div class="flex items-center">
                <i class="ri-check-line ri-lg mr-3"></i>
                <div>
                  <h4 class="font-semibold">Message Sent Successfully!</h4>
                  <p class="text-sm opacity-90">Thank you for reaching out. I'll get back to you soon.</p>
                </div>
              </div>
            </div>

            <form id="contactForm" novalidate>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="form-group">
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Name <span class="text-red-500">*</span></label
                  >
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    minlength="2"
                    maxlength="50"
                    class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="John Doe"
                  />
                  <div class="error-message" id="nameError"></div>
                </div>
                <div class="form-group">
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-2"
                    >Your Email <span class="text-red-500">*</span></label
                  >
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                    placeholder="<EMAIL>"
                  />
                  <div class="error-message" id="emailError"></div>
                </div>
              </div>
              <div class="form-group mb-6">
                <label
                  for="subject"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Subject <span class="text-red-500">*</span></label
                >
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  required
                  minlength="5"
                  maxlength="100"
                  class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Project Inquiry"
                />
                <div class="error-message" id="subjectError"></div>
              </div>
              <div class="form-group mb-6">
                <label
                  for="message"
                  class="block text-sm font-medium text-gray-700 mb-2"
                  >Message <span class="text-red-500">*</span></label
                >
                <textarea
                  id="message"
                  name="message"
                  rows="5"
                  required
                  minlength="10"
                  maxlength="1000"
                  class="form-input w-full px-4 py-3 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="Your message here..."
                ></textarea>
                <div class="character-counter" id="messageCounter">0 / 1000 characters</div>
                <div class="error-message" id="messageError"></div>
              </div>
              <button
                type="submit"
                id="submitButton"
                class="submit-button bg-primary hover:bg-[#13a696] text-white font-medium py-3 px-8 !rounded-button transition-all duration-300 shadow-md hover:shadow-lg whitespace-nowrap"
              >
                <span class="loading-spinner"></span>
                <span class="button-text">Send Message</span>
              </button>
            </form>
          </div>
          <!-- Contact Info -->
          <div class="md:w-1/3">
            <div class="bg-white rounded-lg shadow-md p-8 mb-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Contact Information
              </h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-map-pin-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Location</h4>
                    <p class="text-gray-600">Sagay City Negros Occidental</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-mail-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Email</h4>
                    <p class="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full text-primary mr-4"
                  >
                    <i class="ri-phone-line ri-lg"></i>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Phone</h4>
                    <p class="text-gray-600">+63 ************</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">
                Follow Me
              </h3>
              <div class="flex space-x-4">
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-github-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-facebook-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-twitter-fill ri-lg"></i>
                </a>
                <a
                  href="#"
                  class="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-blue-100 rounded-full text-gray-600 hover:text-primary transition-colors"
                >
                  <i class="ri-instagram-fill ri-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 text-white py-12 relative overflow-hidden">
      <!-- Scroll to Top Button -->
      <button
        id="scrollTopBtn"
        class="fixed bottom-8 right-8 w-10 h-10 bg-primary hover:bg-[#13a696] text-white rounded-full shadow-xl flex items-center justify-center transition-all duration-300 z-50 opacity-100 visible hover:scale-110"
      >
        <i class="ri-arrow-up-s-line ri-xl"></i>
      </button>

      <div class="container mx-auto px-6 md:px-12">
        <div class="flex flex-col md:flex-row justify-between items-left">
          <div class="mb-6 md:mb-0">
            <a href="#" class="logo-container flex items-center">
              <div class="logo-ia footer-logo">
                <span class="logo-i">I</span>
                <span class="logo-a">A</span>
              </div>
            </a>
            <p class="text-gray-400 mt-2">
              Frontend Developer & UI/UX Designer
            </p>
          </div>
          <div
            class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-8"
          >
            <a
              href="#hero"
              class="text-gray-300 hover:text-white transition-colors"
              >Home</a
            >
            <a
              href="#about"
              class="text-gray-300 hover:text-white transition-colors"
              >About</a
            >
            <a
              href="#projects"
              class="text-gray-300 hover:text-white transition-colors"
              >Projects</a
            >
            <a
              href="#skills"
              class="text-gray-300 hover:text-white transition-colors"
              >Skills</a
            >
            <a
              href="#contact"
              class="text-gray-300 hover:text-white transition-colors"
              >Contact</a
            >
          </div>
        </div>
        <hr class="border-gray-700 my-8" />
        <div class="text-center text-gray-400">
          <p>© 2025 iArcillas. All rights reserved.</p>
          <p class="mt-2">Last updated: May 29, 2025</p>
        </div>
      </div>
    </footer>
    <!-- Scripts -->
    <script id="navigationBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");
        const mobileMenu = document.getElementById("mobileMenu");
        const mobileMenuIcon = mobileMenuBtn.querySelector("i");
        // Toggle mobile menu
        mobileMenuBtn.addEventListener("click", function () {
          mobileMenu.classList.toggle("hidden");
          const isOpen = !mobileMenu.classList.contains("hidden");
          mobileMenuIcon.className = isOpen
            ? "ri-close-line ri-lg"
            : "ri-menu-line ri-lg";
        });
        // Close mobile menu when clicking menu items
        mobileMenu.querySelectorAll("a").forEach((link) => {
          link.addEventListener("click", function () {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          });
        });
        // Close mobile menu on window resize
        window.addEventListener("resize", function () {
          if (window.innerWidth >= 768) {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          }
        });

        // Close mobile menu when clicking outside
        document.addEventListener("click", function (event) {
          const nav = document.querySelector("nav");
          const isClickInsideNav = nav.contains(event.target);
          const isMenuOpen = !mobileMenu.classList.contains("hidden");

          if (!isClickInsideNav && isMenuOpen) {
            mobileMenu.classList.add("hidden");
            mobileMenuIcon.className = "ri-menu-line ri-lg";
          }
        });
      });
    </script>
    <script id="scrollBehavior">
      document.addEventListener("DOMContentLoaded", function () {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const targetId = this.getAttribute("href");
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
              window.scrollTo({
                top: targetElement.offsetTop,
                behavior: "smooth",
              });
            }
          });
        });
        // Intersection Observer for scroll animations
        const animateOnScroll = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                entry.target.classList.add("visible");
              }
            });
          },
          {
            threshold: 0.1,
          },
        );
        // Add animation classes and observe elements
        document.querySelectorAll("section").forEach((section) => {
          section.classList.add("animate-fade-up");
          animateOnScroll.observe(section);
        });
        document
          .querySelectorAll(".project-card, .skill-card")
          .forEach((element, index) => {
            element.classList.add("animate-fade-up", "stagger-animation");
            element.style.setProperty("--animation-order", index);
            animateOnScroll.observe(element);
          });
        document.querySelectorAll("img").forEach((img) => {
          img.classList.add("animate-fade-in");
          animateOnScroll.observe(img);
        });
        // Add hover animations to cards and buttons
        document.querySelectorAll(".bg-white.rounded-lg").forEach((card) => {
          card.classList.add("scale-hover");
        });
        // Add nav link hover effect
        document.querySelectorAll(".text-gray-600").forEach((link) => {
          if (link.tagName === "A") {
            link.classList.add("nav-link");
          }
        });
      });
    </script>
    <script id="scrollToTopButton">
      document.addEventListener("DOMContentLoaded", function () {
        const scrollTopBtn = document.getElementById("scrollTopBtn");

        // Initially hide the button
        scrollTopBtn.style.display = "none";

        // Show/hide button based on scroll position
        window.addEventListener("scroll", function() {
          if (window.scrollY > 2000) {
            scrollTopBtn.style.display = "flex";
          } else {
            scrollTopBtn.style.display = "none";
          }
        });

        // Scroll to top when button is clicked
        scrollTopBtn.addEventListener("click", function () {
          window.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        });
      });
    </script>
    <script id="progressBarScript">
      document.addEventListener("DOMContentLoaded", function() {
        const progressBar = document.getElementById("progressBar");

        window.addEventListener("scroll", function() {
          const windowHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
          const scrolled = (window.scrollY / windowHeight) * 100;
          progressBar.style.width = scrolled + "%";
        });


         // Add typing effect to hero title (optional enhancement)
        const heroTitle = document.querySelector('.hero h1');
        if (heroTitle) {
            const text = heroTitle.textContent;
            heroTitle.textContent = '';
            let i = 0;

            const typeWriter = () => {
                if (i < text.length) {
                    heroTitle.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };

            // Start typing effect after page load
            setTimeout(typeWriter, 500);
        }

      });
    </script>
    <script id="parallaxEffect">
      document.addEventListener("DOMContentLoaded", function() {
        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('#hero');
            const heroContent = document.querySelector('#hero .container');

            if (hero && heroContent) {
                heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });
      });
    </script>
    <script id="darkModeToggle">
      document.addEventListener("DOMContentLoaded", function() {
        const themeToggle = document.getElementById('themeToggle');
        const mobileThemeToggle = document.getElementById('mobileThemeToggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // Set initial theme
        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
          html.classList.add('dark');
        }

        // Toggle theme function
        function toggleTheme() {
          html.classList.toggle('dark');
          const isDark = html.classList.contains('dark');
          localStorage.setItem('theme', isDark ? 'dark' : 'light');
        }

        // Add event listeners to both toggles
        if (themeToggle) {
          themeToggle.addEventListener('click', toggleTheme);
        }

        if (mobileThemeToggle) {
          mobileThemeToggle.addEventListener('click', toggleTheme);
        }

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
          if (!localStorage.getItem('theme')) {
            if (e.matches) {
              html.classList.add('dark');
            } else {
              html.classList.remove('dark');
            }
          }
        });
      });
    </script>
    <script id="enhancedContactForm">
      document.addEventListener("DOMContentLoaded", function() {
        // EmailJS Configuration
        const EMAILJS_CONFIG = {
          publicKey: '3MjPixLe0W6PUT5Vt', // Replace with your EmailJS public key
          serviceId: 'service_wxcd4rn', // Replace with your EmailJS service ID
          templateId: 'template_fqlumpo' // Replace with your EmailJS template ID
        };

        // Initialize EmailJS
        emailjs.init(EMAILJS_CONFIG.publicKey);

        const form = document.getElementById('contactForm');
        const submitButton = document.getElementById('submitButton');
        const successMessage = document.getElementById('successMessage');
        const messageTextarea = document.getElementById('message');
        const messageCounter = document.getElementById('messageCounter');

        // Form validation rules
        const validationRules = {
          name: {
            required: true,
            minLength: 2,
            maxLength: 50,
            pattern: /^[a-zA-Z\s]+$/,
            message: 'Name must be 2-50 characters and contain only letters and spaces'
          },
          email: {
            required: true,
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address'
          },
          subject: {
            required: true,
            minLength: 5,
            maxLength: 100,
            message: 'Subject must be 5-100 characters long'
          },
          message: {
            required: true,
            minLength: 10,
            maxLength: 1000,
            message: 'Message must be 10-1000 characters long'
          }
        };

        // Update character counter
        function updateCharacterCounter() {
          const length = messageTextarea.value.length;
          const maxLength = 1000;
          messageCounter.textContent = `${length} / ${maxLength} characters`;

          // Update counter color based on length
          messageCounter.classList.remove('warning', 'error');
          if (length > maxLength * 0.9) {
            messageCounter.classList.add('warning');
          }
          if (length > maxLength) {
            messageCounter.classList.add('error');
          }
        }

        // Validate individual field
        function validateField(fieldName, value) {
          const rules = validationRules[fieldName];
          const errors = [];

          if (rules.required && !value.trim()) {
            errors.push(`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
          }

          if (value.trim()) {
            if (rules.minLength && value.length < rules.minLength) {
              errors.push(`Minimum ${rules.minLength} characters required`);
            }

            if (rules.maxLength && value.length > rules.maxLength) {
              errors.push(`Maximum ${rules.maxLength} characters allowed`);
            }

            if (rules.pattern && !rules.pattern.test(value)) {
              errors.push(rules.message);
            }
          }

          return errors;
        }

        // Show field error
        function showFieldError(fieldName, errors) {
          const field = document.getElementById(fieldName);
          const errorElement = document.getElementById(fieldName + 'Error');

          field.classList.remove('success', 'error');
          errorElement.classList.remove('show');

          if (errors.length > 0) {
            field.classList.add('error');
            errorElement.textContent = errors[0];
            errorElement.classList.add('show');
            return false;
          } else if (field.value.trim()) {
            field.classList.add('success');
          }

          return true;
        }

        // Real-time validation
        function setupRealTimeValidation() {
          Object.keys(validationRules).forEach(fieldName => {
            const field = document.getElementById(fieldName);

            field.addEventListener('blur', function() {
              const errors = validateField(fieldName, this.value);
              showFieldError(fieldName, errors);
            });

            field.addEventListener('input', function() {
              // Clear error state on input
              if (this.classList.contains('error')) {
                const errors = validateField(fieldName, this.value);
                if (errors.length === 0) {
                  showFieldError(fieldName, []);
                }
              }
            });
          });
        }

        // Validate entire form
        function validateForm() {
          let isValid = true;
          const formData = new FormData(form);

          Object.keys(validationRules).forEach(fieldName => {
            const value = formData.get(fieldName) || '';
            const errors = validateField(fieldName, value);
            const fieldValid = showFieldError(fieldName, errors);
            if (!fieldValid) isValid = false;
          });

          return isValid;
        }

        // Submit form using EmailJS
        async function submitForm(formData) {
          try {
            // Prepare template parameters for EmailJS
            const templateParams = {
              from_name: formData.get('name'),
              from_email: formData.get('email'),
              subject: formData.get('subject'),
              message: formData.get('message'),
              to_name: 'Ivan Jay Arcillas', // Your name
              reply_to: formData.get('email')
            };

            // Send email using EmailJS
            const response = await emailjs.send(
              EMAILJS_CONFIG.serviceId,
              EMAILJS_CONFIG.templateId,
              templateParams
            );

            console.log('Email sent successfully:', response);
            return { success: true, response };

          } catch (error) {
            console.error('EmailJS error:', error);
            throw new Error('Failed to send email: ' + error.text || error.message);
          }
        }

        // Handle form submission
        form.addEventListener('submit', async function(e) {
          e.preventDefault();

          if (!validateForm()) {
            return;
          }

          // Show loading state
          submitButton.classList.add('loading');
          submitButton.disabled = true;

          try {
            const formData = new FormData(form);
            const result = await submitForm(formData);

            if (result.success) {
              // Show success message
              successMessage.classList.add('show');
              form.reset();
              updateCharacterCounter();

              // Clear all field states
              document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('success', 'error');
              });

              // Hide success message after 5 seconds
              setTimeout(() => {
                successMessage.classList.remove('show');
              }, 5000);
            }
          } catch (error) {
            console.error('Form submission error:', error);

            // Show user-friendly error message
            let errorMessage = 'There was an error sending your message. Please try again.';

            if (error.message.includes('Failed to send email')) {
              errorMessage = 'Unable to send email. Please check your internet connection and try again.';
            } else if (error.message.includes('Invalid email')) {
              errorMessage = 'Please check your email address and try again.';
            }

            // Create and show error notification
            showErrorMessage(errorMessage);
          } finally {
            // Remove loading state
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
          }
        });

        // Show error message
        function showErrorMessage(message) {
          // Remove existing error message if any
          const existingError = document.querySelector('.error-notification');
          if (existingError) {
            existingError.remove();
          }

          // Create error notification
          const errorDiv = document.createElement('div');
          errorDiv.className = 'error-notification';
          errorDiv.innerHTML = `
            <div class="flex items-center">
              <i class="ri-error-warning-line ri-lg mr-3"></i>
              <div>
                <h4 class="font-semibold">Error</h4>
                <p class="text-sm opacity-90">${message}</p>
              </div>
            </div>
          `;

          // Insert before the form
          form.parentNode.insertBefore(errorDiv, form);

          // Show with animation
          setTimeout(() => errorDiv.classList.add('show'), 100);

          // Auto-hide after 5 seconds
          setTimeout(() => {
            errorDiv.classList.remove('show');
            setTimeout(() => errorDiv.remove(), 300);
          }, 5000);
        }

        // Initialize
        setupRealTimeValidation();
        messageTextarea.addEventListener('input', updateCharacterCounter);
        updateCharacterCounter();
      });
    </script>
    <script id="projectsSwiper">
      document.addEventListener("DOMContentLoaded", function() {
        // Initialize Projects Swiper
        const projectsSwiper = new Swiper('.projects-swiper', {
          // Basic settings
          slidesPerView: 1,
          spaceBetween: 30,
          loop: true,
          autoplay: {
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          },

          // Navigation arrows
          navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          },

          // Pagination dots
          pagination: {
            el: '.swiper-pagination',
            clickable: true,
            dynamicBullets: true,
          },

          // Responsive breakpoints
          breakpoints: {
            // Mobile
            320: {
              slidesPerView: 1,
              spaceBetween: 20,
            },
            // Tablet
            768: {
              slidesPerView: 2,
              spaceBetween: 30,
            },
            // Desktop
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
            // Large Desktop
            1280: {
              slidesPerView: 3,
              spaceBetween: 40,
            }
          },

          // Effects
          effect: 'slide',
          speed: 600,

          // Accessibility
          a11y: {
            enabled: true,
            prevSlideMessage: 'Previous project',
            nextSlideMessage: 'Next project',
            paginationBulletMessage: 'Go to project {{index}}',
          },

          // Keyboard control
          keyboard: {
            enabled: true,
            onlyInViewport: true,
          },

          // Mouse wheel control
          mousewheel: {
            enabled: false,
          },

          // Touch settings
          touchRatio: 1,
          touchAngle: 45,
          grabCursor: true,

          // Lazy loading
          lazy: {
            loadPrevNext: true,
            loadPrevNextAmount: 1,
          },

          // Events
          on: {
            init: function () {
              console.log('Projects Swiper initialized');
            },
            slideChange: function () {
              // Optional: Add analytics tracking here
              console.log('Slide changed to:', this.activeIndex);
            },
          }
        });

        // Pause autoplay on hover
        const swiperContainer = document.querySelector('.projects-swiper');
        if (swiperContainer) {
          swiperContainer.addEventListener('mouseenter', () => {
            projectsSwiper.autoplay.stop();
          });

          swiperContainer.addEventListener('mouseleave', () => {
            projectsSwiper.autoplay.start();
          });
        }

        // Optional: Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
          if (e.key === 'ArrowLeft') {
            projectsSwiper.slidePrev();
          } else if (e.key === 'ArrowRight') {
            projectsSwiper.slideNext();
          }
        });
      });
    </script>

  </body>
</html>
